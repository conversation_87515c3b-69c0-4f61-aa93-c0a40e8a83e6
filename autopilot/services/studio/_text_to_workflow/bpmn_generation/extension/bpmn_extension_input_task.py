import json

from services.studio._text_to_workflow.bpmn_generation import fps_client
from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnExtensionInputToolResult,
    BpmnRequestContext,
    Connector,
    ExtensionInputRequest,
    ExtensionType,
    SolutionResource,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()


class BpmnExtensionInputTask(BpmnBaseTask):
    def __init__(self):
        super().__init__("extension/extension_input.yaml")

    async def generate(self, context: BpmnRequestContext) -> ToolResult:
        # NOT IN USE YET. For chat.
        output = await self._internal_generate(context.user_request, context.current_bpmn, context.extension_input_schema)

        return BpmnExtensionInputToolResult(
            tool=Tool.EXTENSION,
            explanation=output["explanation"],
            inputJson=output["inputJson"],
        )

    async def _internal_generate(self, user_request: str, current_bpmn: str, input_schema: str | None) -> dict:
        if not input_schema:
            return {"explanation": "No input schema provided", "inputJson": ""}

        system_prompt_template = self.config["prompt"]["system"]
        formatted_system_prompt = system_prompt_template.format(
            input_schema=self.config["prompt"]["input_schema"],
            output_schema=self.config["prompt"]["output_schema"],
            # examples=self.config["prompt"]["examples"],
        )

        user_prompt_template = self.config["prompt"]["user"]
        formatted_user_prompt = user_prompt_template.format(
            userRequest=user_request,
            currentBpmn=current_bpmn,
            inputSchema=input_schema,
        )

        result, _ = await self._call_llm(formatted_system_prompt, formatted_user_prompt, "extension_input_generation_bpmn_model")
        # LOGGER.info(f"Result of extension input is : [{result}]")
        str_result = result.strip("```json\n").strip("\n```")
        return json.loads(str_result)

    async def extension_input_generate(self, context: BpmnRequestContext, request: ExtensionInputRequest) -> None:
        """
        Generate the input for the extension based on the request, assign value to the inputJson.
        """

        ext_type = request.extension_type
        element_ext_items = request.items

        if ext_type not in [
            ExtensionType.RPA_WORKFLOW,
            ExtensionType.AGENT,
            ExtensionType.API_WORKFLOW,
            ExtensionType.AGENTIC_PROCESS,
            ExtensionType.QUEUE,
            ExtensionType.ACTION,
            ExtensionType.CONNECTOR,
        ]:
            raise NotImplementedError(f"Support for extension type: {ext_type} is not implemented yet.")

        for element_item in element_ext_items:
            resources = element_item.suggestions

            if all(isinstance(resource, SolutionResource) for resource in resources):
                # tasks = [self._get_solution_resource_inputs(context, resource, ext_type) for resource in resources if isinstance(resource, SolutionResource)]
                # results = await asyncio.gather(*tasks)

                results = []
                for resource in resources:
                    if isinstance(resource, SolutionResource):
                        result = await self._get_solution_resource_inputs(context, resource, ext_type)
                        results.append(result)

                for resource_item, res in zip(resources, results, strict=False):
                    resource_item.inputJson = res["inputJson"]

            elif all(isinstance(item, Connector) for item in resources):
                # TODO: support connector type
                raise NotImplementedError(f"Support for extension type: {ext_type} is not implemented yet.")
            else:
                raise ValueError("Expected all items to be the same type, but it is not, please check the request provider.")

    async def _get_solution_resource_inputs(self, context: BpmnRequestContext, item: SolutionResource, extension_type: ExtensionType) -> dict[str, str]:
        reference_key = await fps_client.get_extension_reference_key(context.request_context, context.solution_id, item)
        if not reference_key:
            LOGGER.warning(f"Failed to get reference key for solution resource {item.id}")
            return {"explanation": "Failed to get reference key for solution resource", "inputJson": ""}
        input_schema_escaped = await fps_client.get_extension_configuration_input_schema_raw(
            context.request_context, context.solution_id, reference_key, extension_type
        )

        unescaped = input_schema_escaped.encode().decode("unicode_escape")
        parsed_json = json.loads(unescaped)
        pretty_json = json.dumps(parsed_json)

        res = await self._internal_generate(context.user_request, context.current_bpmn, pretty_json)
        return res
